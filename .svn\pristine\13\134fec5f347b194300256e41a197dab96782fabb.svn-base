<?php

return array (
  1 => 
  array (
    'name' => 'appkey',
    'title' => 'AppKey',
    'type' => 'string',
    'content' => 
    array (
    ),
    'value' => '07Y5L5o9WovGkWAt0ZKbWnhk',
    'rule' => 'required',
    'msg' => '',
    'tip' => '请前往百度控制台 > 访问管理 > AppKey密钥',
    'ok' => '',
    'extend' => '',
  ),
  2 => 
  array (
    'name' => 'secretkey',
    'title' => 'SecretKey',
    'type' => 'string',
    'content' => 
    array (
    ),
    'value' => 'cSIhOZPeC5KzjjI3phwXodBhVv64UXEi',
    'rule' => 'required',
    'msg' => '',
    'tip' => '请前往百度控制台 > 访问管理 > secretkey密钥',
    'ok' => '',
    'extend' => '',
  ),
  3 => 
  array (
    'name' => 'appid',
    'title' => 'AppId',
    'type' => 'string',
    'content' => 
    array (
    ),
    'value' => '19644853',
    'rule' => 'required',
    'msg' => '',
    'tip' => '请前往百度控制台 > 访问管理 > AppId',
    'ok' => '',
    'extend' => '',
  ),
  4 => 
  array (
    'name' => 'idmatch',
    'title' => '身份验证接口',
    'type' => 'string',
    'content' => 
    array (
    ),
    'value' => 'https://aip.baidubce.com/rest/2.0/ocr/v1/form',
    'rule' => 'required',
    'msg' => '',
    'tip' => '身份验证接口',
    'ok' => '',
    'extend' => '',
  ),
  5 => 
  array (
    'name' => 'idcard',
    'title' => 'ocr识别接口',
    'type' => 'string',
    'content' => 
    array (
    ),
    'value' => 'https://aip.baidubce.com/rest/2.0/ocr/v1/form',
    'rule' => 'required',
    'msg' => '',
    'tip' => '身份验证接口',
    'ok' => '',
    'extend' => '',
  ),
  6 => 
  array (
    'name' => 'isapi',
    'title' => '前台是否通过API审核',
    'type' => 'radio',
    'content' => 
    array (
      1 => '是',
      0 => '否',
    ),
    'value' => '1',
    'rule' => 'required',
    'msg' => '',
    'tip' => '前台是否通过API审核',
    'ok' => '',
    'extend' => '',
  ),
  7 => 
  array (
    'name' => 'replay',
    'title' => '限制单日api审核次数',
    'type' => 'string',
    'content' => 
    array (
    ),
    'value' => '10',
    'rule' => 'required',
    'msg' => '',
    'tip' => '',
    'ok' => '',
    'extend' => '',
  ),
  8 => 
  array (
    'name' => '__tips__',
    'title' => '温馨提示',
    'type' => 'array',
    'content' => 
    array (
    ),
    'value' => '请在百度云管理中心获取SecretId与SecretKey,AppId,其他选项默认即可',
    'rule' => '',
    'msg' => '',
    'tip' => '身份证验证参数配置',
    'ok' => '',
    'extend' => '',
  ),
);
