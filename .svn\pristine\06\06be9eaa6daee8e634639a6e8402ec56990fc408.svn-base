/* NKeditor 5.0.3 (2018-10-25), Copyright (C) r9it.com,*/
!function(a){if(!window.applicationCache)throw new Error("您当前的浏览器不支持HTML5,请先升级浏览器才能使用该上传插件!");a.fn.imageCrop=function(b,c){a(this).on("load",function(){var d,e,f,g,h=this.width/this.height,i=b/c;h>=i?(e=c,d=b*h,g=0,f=(d-b)/2):(d=b,e=c/h,f=0,g=0),a(this).css({position:"absolute",top:-g+"px",left:-f+"px",width:d+"px",height:e+"px"})})},a.fn.draggable=function(b){var c={handler:null};b=a.extend(c,b);var d=this;a(b.handler).mousedown(function(b){var c=b.pageX-a(d).position().left,e=b.pageY-a(d).position().top;a(document).mousemove(function(b){window.getSelection?window.getSelection().removeAllRanges():document.selection.empty(),a(d).css({top:b.pageY-e+"px",left:b.pageX-c+"px"})})}).mouseup(function(){a(document).unbind("mousemove")})},void 0==Array.prototype.remove&&(Array.prototype.remove=function(a){for(var b=0;b<this.length;b++)if(this[b]==a){this.splice(b,1);break}}),void 0==Array.prototype.uinque&&(Array.prototype.uinque=function(){for(var a,b=[],c={},d=0;null!=(a=this[d]);d++)c[a]||(b.push(a),c[a]=!0);return b}),window.BUpload=function(c){function d(){var d=new b;d.append('<div class="uedbody ke-animated"><div class="ued_title">'),d.append('<div class="uedbar"><span>'+c.lang.title+'</span></div><div class="close_btn icon" title="'+c.lang.closeText+'"></div>'),d.append('</div><div class="wrapper"><div id="wra_head" class="wra_head"><span class="tab tab-upload focus" tab="upload-panel">'+c.lang.localUpload+"</span>"),null!=c.list_url&&d.append('<span class="tab tab-online" tab="online">'+c.lang.fileServer+"</span>"),d.append('</div><div class="wra_body"><div class="tab-panel upload-panel"><div class="wra_pla"><div class="upload-image-placeholder">'),d.append('<div class="btn btn-primary image-select">'+c.lang.selectFile+'</div><input type="file" name="'+c.src+'" class="webuploader-element-invisible" multiple="multiple" accept="'+n()+'">'),d.append('</div></div><div class="image-list-box" style="display: none;"><div class="wra_bar"><div class="info fl"></div>'),d.append('<div class="fr"><span class="btn btn-default btn-continue-add">'+c.lang.continueAdd+'</span><span class="btn btn-primary btn-start-upload">'+c.lang.startUpload+"</span></div></div>"),d.append('<ul class="filelist"></ul></div></div><div class="tab-panel online"><div class="imagelist"><ul class="list clearfix"></ul><div class="no-data"></div></div></div>'),d.append('<div class="tab-panel searchbox"><div class="search-bar"><input class="searTxt" type="text" placeholder="'+c.lang.searchPlaceholder+'" />'),d.append('<input value="'+c.lang.searchBtn+'" class="btn btn-primary btn-search" type="button" /><input value="'+c.lang.searchClear+'" class="btn btn-default btn-reset" type="button" />'),d.append('</div><div class="search-imagelist-box"><ul class="search-list"></ul><div class="no-data"></div></div>'),d.append('</div><div class="loading-icon"></div></div><!-- end of wrapper --></div><div class="wra-btn-group"><span class="btn btn-primary btn-confirm">'+c.lang.confirmBtnText+"</span>"),d.append('<span class="btn btn-default btn-cancel">'+c.lang.cancelBtnText+"</span></div></div>"),u.dialog=a(d.toString()),a("body").append(u.dialog),0==c.top&&(c.top=(a(window).height()-u.dialog.height())/2),u.dialog.css({left:(a(window).width()-u.dialog.width())/2+"px",top:c.top+"px"}),u.dialog.draggable({handler:u.dialog.find(".ued_title")})}function e(){p(".tab").on("click",function(){var b=a(this).attr("tab");p(".tab-panel").hide(),p("."+b).show(),p(".tab").removeClass("focus"),a(this).addClass("focus")}),p(".close_btn").on("click",function(){u.close()}),p(".webuploader-element-invisible").on("change",function(){f(this)}),p(".image-select").on("click",function(){p(".webuploader-element-invisible").trigger("click")}),p(".btn-continue-add").on("click",function(){p(".webuploader-element-invisible").trigger("click")}),p(".btn-start-upload").on("click",function(){if(!u.uploadLock){if(0==u.todoList.length)return c.errorHandler(c.lang.noFileAdded,"error"),!1;a(this).addClass("disabled").text(c.lang.uploading),g(u.todoList.shift())}}),p(".btn-confirm").on("click",function(){return u.todoList.length>0?(c.errorHandler(c.lang.fileNotUpload,"error"),!1):0==u.selectedList.length?(c.errorHandler(c.lang.noFileSelected,"error"),!1):(c.callback(u.selectedList),void u.close())}),p(".btn-cancel").on("click",function(){u.close()}),p(".tab-online").on("click",function(){0==p(".imagelist .list").children().length&&q()}),p(".imagelist").on("scroll",function(){this.scrollTop+this.clientHeight>=this.scrollHeight&&q()})}function f(d){for(var e=d.files,f=u.todoList.length+u.uploadSuccessNum+e.length,g=u.addedFileNumber;g<u.addedFileNumber+e.length;g++){if(f>c.max_filenum)return void c.errorHandler(KindEditor.tmpl(c.lang.uploadLimit,{uploadLimit:c.max_filenum}),"error");var h=new b,i=e[g-u.addedFileNumber];h.append('<li id="img-comtainer-'+v+g+'"><div class="imgWrap">');var k=m(i.name);""==k&&(k="default"),k=k.toLowerCase(),-1=="jpg|jpeg|gif|png|bmp".indexOf(k)?h.append('<span class="icon-placeholder icon-default icon-'+k+'"></span>'):h.append('<img src="'+window.URL.createObjectURL(i)+'" border="0" />'),h.append('</div><div class="file-opt-box clearfix"><span class="remove" index="'+g+'">'+c.lang.remove+'</span><span class="rotateRight">'+c.lang.rotateRight+"</span>"),h.append('<span class="rotateLeft">'+c.lang.rotateLeft+'</span></div><div class="success"></div><div class="error"></div>'),h.append('<div class="progress"><span style="display: none; width: 0px;"></span></div></li>');var l=a(h.toString());l.find(".remove").on("click",function(){a(this).parents("li").remove();for(var b=a(this).attr("index"),c=0;c<u.todoList.length;c++)if(u.todoList[c].index==b){u.totalFilesize-=u.todoList[c].file.size,j(u.uploadSuccessNum+u.todoList.length-1,u.totalFilesize),u.todoList.splice(c,1);break}0==p(".filelist li").length&&(p(".image-list-box").hide(),p(".wra_pla").show())}),l.on("mouseover",function(){a(this).find(".file-opt-box").show()}).on("mouseout",function(){a(this).find(".file-opt-box").hide()}),p(".wra_pla").hide(),p(".image-list-box").show(),p(".filelist").append(l),u.todoList.push({index:g,file:i}),u.totalFilesize+=i.size}u.addedFileNumber+=e.length,j(u.uploadSuccessNum+u.todoList.length,u.totalFilesize),a(".imgWrap img").imageCrop(113,113)}function g(b){if(!l(b))return void h();var d=new XMLHttpRequest;d.open("POST",c.upload_url),d.addEventListener("load",function(d){if("json"==c.data_type){var e=a.parseJSON(d.target.responseText);"000"==e.code?(u.selectedList.push(e.data.url),u.uploadSuccessNum++,a("#img-comtainer-"+v+b.index).find(".file-opt-box").remove(),a("#img-comtainer-"+v+b.index).find(".progress").remove(),a("#img-comtainer-"+v+b.index).find(".success").show()):o(s[e.code],b)}},!1),d.addEventListener("loadend",function(){h()},!1),d.addEventListener("error",function(){o(c.lang.uploadFail,b)},!1),d.upload.addEventListener("progress",function(a){i(a,b)},!1);var e=new FormData;e.append(c.src,b.file),d.send(e)}function h(){if(u.todoList.length){var a=u.todoList.shift();g(a)}else u.uploadLock=!1,p(".btn-start-upload").removeClass("disabled").text(c.lang.startUpload)}function i(b,c){b.lengthComputable&&a("#img-comtainer-"+v+c.index).find(".progress span").css({width:b.loaded/b.total*100+"%",display:"block"})}function j(a,b){var d=KindEditor.tmpl(c.lang.uploadDesc,{numSelect:a,totalSize:k(b),numLeft:c.max_filenum-a});p(".info").text(d)}function k(a){return a/1048576>1?(a/1048576).toFixed(2)+"MB":(a/1024).toFixed(2)+"KB"}function l(a){var b=1024*c.max_filesize;if(b>0&&a.file.size>b)return o(KindEditor.tmpl(c.lang.sizeLimit,{sizeLimit:c.max_filesize}),a),!1;var d=m(a.file.name);return d&&-1!=c.ext_allow.indexOf(d)&&-1==c.ext_refuse.indexOf(d)?!0:(o(KindEditor.tmpl(c.lang.invalidExt,{invalidExt:d}),a),!1)}function m(a){if(!a)return!1;var b=a.lastIndexOf(".");return-1!=b?a.substr(b+1).toLowerCase():!1}function n(){var b=c.ext_allow.split("|"),d=[];return a.each(b,function(a,b){d.push(t[b])}),d.length>1?d.uinque().join(","):"*"}function o(a,b){p("#img-comtainer-"+v+b.index).find(".error").show().text(a)}function p(a){return u.dialog.find(a)}function q(){return c.list_url?u.noRecord?!1:(p(".loading-icon").show(),void a.get(c.list_url,{page:u.page,marker:u.marker,fileType:c.fileType},function(a){if(p(".loading-icon").hide(),"000"==a.code){if(!a.data[0])return void p(".online .no-data").text(c.lang.noDataText).show();u.marker=a.extra,u.page++,r(a.data,"online")}else p(".online .no-data").text(c.lang.noDataText).show(),u.noRecord=!0},"json")):(p(".online .no-data").html('<span class="error">'+c.lang.noListUrl+"</span>").show(),!1)}function r(c,d){a.each(c,function(c,e){var f=new b;f.append("<li>");var g=m(e.thumbURL);""==g&&(g="default"),g=g.toLowerCase();var h=e.width+"x"+e.height;-1=="jpg|jpeg|gif|png|bmp".indexOf(g)?(h=k(e.filesize),f.append('<span class="icon-placeholder icon-'+g+'" data-src="'+e.oriURL+'"></span>')):f.append('<img src="'+e.thumbURL+'" data-src="'+e.oriURL+'" border="0">'),f.append('<span class="ic" data-module="'+d+'"><em class="img-size">'+h+"</em></span></li>");var i=a(f.toString());i.find(".ic").on("click",function(){var b=a(this).prev().data("src");a(this).data("module");a(this).hasClass("selected")?a(this).removeClass("selected"):(a(this).addClass("selected"),u.selectedList.push(b))}),i.find("img").imageCrop(113,113),"online"==d?p(".imagelist .list").append(i):"search"==d&&p(".search-imagelist-box .search-list").append(i)})}c=a.extend({src:"src",upload_url:null,list_url:null,data_type:"json",top:20,fileType:"image",max_filesize:2048,max_filenum:20,no_data_text:"(⊙o⊙)亲，没有多数据了。",ext_allow:"jpg|png|gif|jpeg",ext_refuse:"exe|txt",errorHandler:function(a,b){alert(a)},callback:function(a){console.log(a)}},c);var s={"000":"文件上传成功","001":"文件上传失败","003":"文件大小超出限制","004":"非法文件名后缀"},t={"3gpp":"audio/3gpp, video/3gpp",ac3:"audio/ac3",asf:"allpication/vnd.ms-asf",au:"audio/basic",css:"text/css",csv:"text/csv",doc:"application/msword",dot:"application/msword",dtd:"application/xml-dtd",dwg:"image/vnd.dwg",dxf:"image/vnd.dxf",gif:"image/gif",htm:"text/html",html:"text/html",jp2:"image/jp2",jpe:"image/jpeg",jpeg:"image/jpeg",jpg:"image/jpeg",js:"text/javascript, application/javascript",json:"application/json",mp2:"audio/mpeg, video/mpeg",mp3:"audio/mpeg",mp4:"audio/mp4, video/mp4",mpeg:"video/mpeg",mpg:"video/mpeg",mpp:"application/vnd.ms-project",ogg:"application/ogg, audio/ogg",pdf:"application/pdf",png:"image/png",pot:"application/vnd.ms-powerpoint",pps:"application/vnd.ms-powerpoint",ppt:"application/vnd.ms-powerpoint",rtf:"application/rtf, text/rtf",svf:"image/vnd.svf",tif:"image/tiff",tiff:"image/tiff",txt:"text/plain",wdb:"application/vnd.ms-works",wps:"application/vnd.ms-works",xhtml:"application/xhtml+xml",xlc:"application/vnd.ms-excel",xlm:"application/vnd.ms-excel",xls:"application/vnd.ms-excel",xlt:"application/vnd.ms-excel",xlw:"application/vnd.ms-excel",xml:"text/xml, application/xml",zip:"aplication/zip",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},u={};u.dialog=null,u.todoList=new Array,u.uploadSuccessNum=0,u.selectedList=new Array,u.addedFileNumber=0,u.totalFilesize=0,u.uploadLock=!1,u.page=1,u.marker=null,u.noRecord=!1;var v=Math.ceil(1e12*Math.random());return u.close=function(){u.dialog.remove(),"function"==typeof c.close&&c.close()},d(),e(),u};var b=function(){var a=new Array;b.prototype.append=function(b){a.push(b)},b.prototype.toString=function(){return a.join("")}}}(jQuery);