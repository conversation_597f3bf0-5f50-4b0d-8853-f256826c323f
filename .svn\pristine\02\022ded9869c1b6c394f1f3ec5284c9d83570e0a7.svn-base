<?php
/*
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF <PERSON><PERSON><PERSON><PERSON>TUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRI<PERSON> LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, <PERSON>VEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * This software consists of voluntary contributions made by many individuals
 * and is licensed under the MIT license. For more information, see
 * <http://www.doctrine-project.org>.
 */

namespace Doctrine\Common\Cache;

/**
 * WinCache cache provider.
 *
 * @link   www.doctrine-project.org
 * @since  2.2
 * <AUTHOR> Eberlei <<EMAIL>>
 * <AUTHOR> Blanco <<EMAIL>>
 * <AUTHOR> Wage <<EMAIL>>
 * <AUTHOR> Borschel <<EMAIL>>
 * <AUTHOR> Abdemoulaie <<EMAIL>>
 */
class WinCacheCache extends CacheProvider
{
    /**
     * {@inheritdoc}
     */
    protected function doFetch($id)
    {
        return wincache_ucache_get($id);
    }

    /**
     * {@inheritdoc}
     */
    protected function doContains($id)
    {
        return wincache_ucache_exists($id);
    }

    /**
     * {@inheritdoc}
     */
    protected function doSave($id, $data, $lifeTime = 0)
    {
        return (bool) wincache_ucache_set($id, $data, (int) $lifeTime);
    }

    /**
     * {@inheritdoc}
     */
    protected function doDelete($id)
    {
        return wincache_ucache_delete($id);
    }

    /**
     * {@inheritdoc}
     */
    protected function doFlush()
    {
        return wincache_ucache_clear();
    }

    /**
     * {@inheritdoc}
     */
    protected function doGetStats()
    {
        $info    = wincache_ucache_info();
        $meminfo = wincache_ucache_meminfo();

        return array(
            Cache::STATS_HITS             => $info['total_hit_count'],
            Cache::STATS_MISSES           => $info['total_miss_count'],
            Cache::STATS_UPTIME           => $info['total_cache_uptime'],
            Cache::STATS_MEMORY_USAGE     => $meminfo['memory_total'],
            Cache::STATS_MEMORY_AVAILABLE => $meminfo['memory_free'],
        );
    }
}
