/*!
 * bootstrap-typeahead.js v0.0.5 (http://www.upbootstrap.com)
 * Copyright 2012-2016 Twitter Inc.
 * Licensed under MIT (https://github.com/biggora/bootstrap-ajax-typeahead/blob/master/LICENSE)
 * See Demo: http://plugins.upbootstrap.com/bootstrap-ajax-typeahead
 * Updated: 2016-11-09 04:40:04
 *
 * Modifications by <PERSON> and <PERSON><PERSON>
 */
!function(a){"use strict";var b=function(b,c){a.fn.typeahead.defaults;c.scrollBar&&(c.items=100,c.menu='<ul class="typeahead dropdown-menu" style="max-height:220px;overflow:auto;"></ul>');var d=this;if(d.$element=a(b),d.options=a.extend({},a.fn.typeahead.defaults,c),d.$menu=a(d.options.menu).insertAfter(d.$element),d.eventSupported=d.options.eventSupported||d.eventSupported,d.grepper=d.options.grepper||d.grepper,d.highlighter=d.options.highlighter||d.highlighter,d.lookup=d.options.lookup||d.lookup,d.matcher=d.options.matcher||d.matcher,d.render=d.options.render||d.render,d.onSelect=d.options.onSelect||null,d.sorter=d.options.sorter||d.sorter,d.select=d.options.select||d.select,d.source=d.options.source||d.source,d.displayField=d.options.displayField||d.displayField,d.valueField=d.options.valueField||d.valueField,d.autoSelect=d.options.autoSelect||d.autoSelect,d.options.ajax){var e=d.options.ajax;"string"==typeof e?d.ajax=a.extend({},a.fn.typeahead.defaults.ajax,{url:e}):("string"==typeof e.displayField&&(d.displayField=d.options.displayField=e.displayField),"string"==typeof e.valueField&&(d.valueField=d.options.valueField=e.valueField),d.ajax=a.extend({},a.fn.typeahead.defaults.ajax,e)),d.ajax.url||(d.ajax=null),d.query=""}else d.source=d.options.source,d.ajax=null;d.shown=!1,d.listen()};b.prototype={constructor:b,eventSupported:function(a){var b=a in this.$element;return b||(this.$element.setAttribute(a,"return;"),b="function"==typeof this.$element[a]),b},select:function(){var a=this.$menu.find(".active");if(a.length){var b=a.data("value"),c=this.$menu.find(".active a").text();this.$element.val(this.updater(c)).change(),this.options.onSelect&&this.options.onSelect({value:b,text:c})}return this.hide()},updater:function(a){return a},show:function(){var b=a.extend({},this.$element.position(),{height:this.$element[0].offsetHeight});if(this.$menu.css({top:b.top+b.height,left:b.left}),this.options.alignWidth){var c=a(this.$element[0]).outerWidth();this.$menu.css({width:c})}return this.$menu.show(),this.shown=!0,this},hide:function(){return this.$menu.hide(),this.shown=!1,this},ajaxLookup:function(){function b(){this.ajaxToggleLoadClass(!0),this.ajax.xhr&&this.ajax.xhr.abort();var b=this.ajax.preDispatch?this.ajax.preDispatch(c):{query:c};this.ajax.xhr=a.ajax({url:this.ajax.url,data:b,success:a.proxy(this.ajaxSource,this),type:this.ajax.method||"get",dataType:"json",headers:this.ajax.headers||{}}),this.ajax.timerId=null}var c=a.trim(this.$element.val());return c===this.query?this:(this.query=c,this.ajax.timerId&&(clearTimeout(this.ajax.timerId),this.ajax.timerId=null),!c||c.length<this.ajax.triggerLength?(this.ajax.xhr&&(this.ajax.xhr.abort(),this.ajax.xhr=null,this.ajaxToggleLoadClass(!1)),this.shown?this.hide():this):(this.ajax.timerId=setTimeout(a.proxy(b,this),this.ajax.timeout),this))},ajaxSource:function(a){this.ajaxToggleLoadClass(!1);var b,c=this;if(c.ajax.xhr)return c.ajax.preProcess&&(a=c.ajax.preProcess(a)),c.ajax.data=a,b=c.grepper(c.ajax.data)||[],b.length?(c.ajax.xhr=null,c.render(b.slice(0,c.options.items)).show()):c.shown?c.hide():c},ajaxToggleLoadClass:function(a){this.ajax.loadingClass&&this.$element.toggleClass(this.ajax.loadingClass,a)},lookup:function(a){var b,c=this;return c.ajax?void c.ajaxer():(c.query=c.$element.val(),c.query&&(b=c.grepper(c.source))?(0==b.length&&(b[0]={id:-21,name:"Result not Found"}),c.render(b.slice(0,c.options.items)).show()):c.shown?c.hide():c)},matcher:function(a){return~a.toLowerCase().indexOf(this.query.toLowerCase())},sorter:function(a){if(this.options.ajax)return a;for(var b,c=[],d=[],e=[];b=a.shift();)b.toLowerCase().indexOf(this.query.toLowerCase())?~b.indexOf(this.query)?d.push(b):e.push(b):c.push(b);return c.concat(d,e)},highlighter:function(a){var b=this.query.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&");return a.replace(new RegExp("("+b+")","ig"),function(a,b){return"<strong>"+b+"</strong>"})},render:function(b){var c,d=this,e="string"==typeof d.options.displayField;return b=a(b).map(function(b,f){return"object"==typeof f?(c=e?f[d.options.displayField]:d.options.displayField(f),b=a(d.options.item).data("value",f[d.options.valueField])):(c=f,b=a(d.options.item).data("value",f)),b.find("a").html(d.highlighter(c)),b[0]}),d.autoSelect&&b.first().addClass("active"),this.$menu.html(b),this},grepper:function(b){var c,d,e=this,f="string"==typeof e.options.displayField;if(!(f&&b&&b.length))return null;if(b[0].hasOwnProperty(e.options.displayField))c=a.grep(b,function(a){return d=f?a[e.options.displayField]:e.options.displayField(a),e.matcher(d)});else{if("string"!=typeof b[0])return null;c=a.grep(b,function(a){return e.matcher(a)})}return this.sorter(c)},next:function(b){var c=this.$menu.find(".active").removeClass("active"),d=c.next();if(d.length||(d=a(this.$menu.find("li")[0])),this.options.scrollBar){var e=this.$menu.children("li").index(d);e%8==0&&this.$menu.scrollTop(26*e)}d.addClass("active")},prev:function(a){var b=this.$menu.find(".active").removeClass("active"),c=b.prev();if(c.length||(c=this.$menu.find("li").last()),this.options.scrollBar){var d=this.$menu.children("li"),e=d.length-1,f=d.index(c);(e-f)%8==0&&this.$menu.scrollTop(26*(f-7))}c.addClass("active")},listen:function(){this.$element.on("focus",a.proxy(this.focus,this)).on("blur",a.proxy(this.blur,this)).on("keypress",a.proxy(this.keypress,this)).on("keyup",a.proxy(this.keyup,this)),this.eventSupported("keydown")&&this.$element.on("keydown",a.proxy(this.keydown,this)),this.$menu.on("click",a.proxy(this.click,this)).on("mouseenter","li",a.proxy(this.mouseenter,this)).on("mouseleave","li",a.proxy(this.mouseleave,this))},move:function(a){if(this.shown){switch(a.keyCode){case 9:case 13:case 27:a.preventDefault();break;case 38:a.preventDefault(),this.prev();break;case 40:a.preventDefault(),this.next()}a.stopPropagation()}},keydown:function(b){this.suppressKeyPressRepeat=~a.inArray(b.keyCode,[40,38,9,13,27]),this.move(b)},keypress:function(a){this.suppressKeyPressRepeat||this.move(a)},keyup:function(a){switch(a.keyCode){case 40:case 38:case 16:case 17:case 18:break;case 9:case 13:if(!this.shown)return;this.select();break;case 27:if(!this.shown)return;this.hide();break;default:this.ajax?this.ajaxLookup():this.lookup()}a.stopPropagation(),a.preventDefault()},focus:function(a){this.focused=!0},blur:function(a){this.focused=!1,!this.mousedover&&this.shown&&this.hide()},click:function(a){a.stopPropagation(),a.preventDefault(),this.select(),this.$element.focus()},mouseenter:function(b){this.mousedover=!0,this.$menu.find(".active").removeClass("active"),a(b.currentTarget).addClass("active")},mouseleave:function(a){this.mousedover=!1,!this.focused&&this.shown&&this.hide()},destroy:function(){this.$element.off("focus",a.proxy(this.focus,this)).off("blur",a.proxy(this.blur,this)).off("keypress",a.proxy(this.keypress,this)).off("keyup",a.proxy(this.keyup,this)),this.eventSupported("keydown")&&this.$element.off("keydown",a.proxy(this.keydown,this)),this.$menu.off("click",a.proxy(this.click,this)).off("mouseenter","li",a.proxy(this.mouseenter,this)).off("mouseleave","li",a.proxy(this.mouseleave,this)),this.$element.removeData("typeahead")}},a.fn.typeahead=function(c){return this.each(function(){var d=a(this),e=d.data("typeahead"),f="object"==typeof c&&c;e||d.data("typeahead",e=new b(this,f)),"string"==typeof c&&e[c]()})},a.fn.typeahead.defaults={source:[],items:10,scrollBar:!1,alignWidth:!0,menu:'<ul class="typeahead dropdown-menu"></ul>',item:'<li><a href="#"></a></li>',valueField:"id",displayField:"name",autoSelect:!0,onSelect:function(){},ajax:{url:null,timeout:300,method:"get",triggerLength:1,loadingClass:null,preDispatch:null,preProcess:null}},a.fn.typeahead.Constructor=b,a(function(){a("body").on("focus.typeahead.data-api",'[data-provide="typeahead"]',function(b){var c=a(this);c.data("typeahead")||(b.preventDefault(),c.typeahead(c.data()))})})}(window.jQuery);