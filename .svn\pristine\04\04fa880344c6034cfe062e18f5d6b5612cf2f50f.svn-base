<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>惠满财</title>

    <!-- 最新版本的 Bootstrap 核心 CSS 文件 -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@3.3.7/dist/css/bootstrap.min.css" integrity="sha384-BVYiiSIFeK1dGmJRAkycuHAHRg32OmUcww7on3RYdg4Va+PmSTsz/K68vbdEjh4u" crossorigin="anonymous">

<!-- 可选的 Bootstrap 主题文件（一般不用引入） -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@3.3.7/dist/css/bootstrap-theme.min.css" integrity="sha384-rHyoN1iRsVXV4nD0JutlnGaslCJuC7uwjduW9SVrLvRYooPp2bWYgmgJQIXwl/Sp" crossorigin="anonymous">
<link rel="stylesheet" href="__CDN__/assets/css/index_web.css">


</head>
<body>
    <div class="header">
        <div class="header_left">
            <img src="__CDN__/assets/img/web/logo.png">
            <div class="right_word">
                <div class="title">惠满财</div>
                <div class="title_sub">上海惠满财数字科技有限公司</div>
            </div>
        </div>
        <div class="header_right">
            <div class="download"><a href="/">首页</a></div>
            <!-- <div class="download"><a href="https://b.pingan.com.cn/ca/pc/index.html#/First?channel=WX&onlineSQFlag=N&sign=f5224879-33c6-47fb-8b67-7943675b50ef1845bb4669a3fe75a6226f13557bbe0e&ccp=1a3a9a30aEM02atcg2ayys1amck1athk1&cardCatenaNo=01a02a&versionNo=R10310&scc=880000051&deleteCardinfo=Y&simpListAB=null&hideAddrFlag=N" target="_blank">信用卡办理</a></div> -->
            <div class="download"><a href="__CDN__/template/报表模板.zip">模版下载</a></div>
            {if condition="$username neq ''"}
            <div class="upload"><span class="glyphicon glyphicon-folder-open"></span><a href="{:url('web/upload')}">文件上传</a></div>
            {/if}
            {if condition="$username neq ''"}
            <div class="login">
                {$username}
            </div>
            &nbsp;&nbsp;
            <div class="login">
                <a href="{:url('web/logout')}">退出</a>
            </div>
            {else /}<div class="login"><a href="{:url('web/login')}">注册/登录</a></div>
            {/if}

        </div>
    </div>
    <div class="line"></div>
    <div class="banner">
        <div id="wrapper">
			<div id="slider-wrap">
                <ul id="slider">
                    {foreach name="slide_data" item="vo"}
                    <li data-color="#1abc9c">
                        <a href="{:url('web/detail')}?id={$vo.id}&type=2"><img src="{$vo.image}"></a>
                    </li>
                    {/foreach}
                </ul>
                
                <!--controls-->
                <div class="btns" id="next"><i class="glyphicon glyphicon-menu-left"></i></div>
                <div class="btns" id="previous"><i class="glyphicon glyphicon-menu-right"></i></div>
                <div id="counter"></div>
                <div id="pagination-wrap">
                <ul>
                </ul>
                </div>
                <!--controls-->  
                        
            </div>
		</div>

    </div>
    <div class="middle">
        <div class="zixun">
            <!-- <div class="left">
                <ul>
                    {foreach name="list" item="vo"}
                    <li>
                        <a href="{:url('web/detail')}?id={$vo.id}&type=1" class="leftbox">
                            <div class="title" title="{$vo.title}">{$vo.title}</div>
                            <div class="content" title="{$vo.title}">{$vo.title}</div>
                            <div class="sub_content"><span class="addtime">{$vo.publishtime_text}</span><span class="readnum">&nbsp;&nbsp;{$vo.views}阅读</span></div>
                        </a>
                        <img style="width: 150px;height: 150px;" src="{$vo.image}" alt="{$vo.title}">
                    </li>
                    {/foreach}
                </ul>
                <div class="more">加载更多</div>
                <div class="loading"><img src="__CDN__/assets/img/web/loading.gif"></div>
            </div> -->
            <!-- <div>
                <div class="right">
                    <div class="headertitle">
                        <img src="__CDN__/assets/img/web/zdxs.png">
                        <div class="glyphicon glyphicon-menu-right"></div>
                    </div>
                    <div class="rightlist">
                        <ul>
                            {foreach name="data" item="vo"}
                            <li>
                                <a href="{:url('web/detail')}?id={$vo.id}&type=1">
                                    <div class="title" title="{$vo.title}">{$vo.title}</div>
                                    <div class="content" title="{$vo.title}">{$vo.title}</div>
                                </a>
                            </li>
                            {/foreach}
                        </ul>
                    </div>
                </div>
                <div class="erweima">
                    <img src="__CDN__/assets/img/web/erweima.jpg">
                    <div class="guanzhu">
                        <div class="gongzhonghao">关注惠满财公众号</div>
                        <div class="des">随时随地获取更多财务信息</div>
                    </div>
                </div>
                <div class="erweima">
                    <img src="__CDN__/assets/img/web/apk.png">
                    <div class="guanzhu">
                        <div class="gongzhonghao">安卓手机扫码下载</div>
                        <!--<div class="des">让世界没有不懂财务的人</div>-->
                    </div>
                </div> -->
                <!-- <div class="pingan">
                    <a href="https://b.pingan.com.cn/ca/pc/index.html#/First?channel=WX&onlineSQFlag=N&sign=f5224879-33c6-47fb-8b67-7943675b50ef1845bb4669a3fe75a6226f13557bbe0e&ccp=1a3a9a30aEM02atcg2ayys1amck1athk1&cardCatenaNo=01a02a&versionNo=R10310&scc=880000051&deleteCardinfo=Y&simpListAB=null&hideAddrFlag=N" target="_blank"><img src="__CDN__/assets/img/web/pingan.png"></a>
                </div> -->
            </div>
        </div>
    </div>
    <div class="footer">
        <a href="http://www.beian.miit.gov.cn/" target="_blank" style="color: #bdbdbd">沪ICP备20011006号</a> ©版权所有CopyRight 2009-2020 上海惠满财数字科技有限公司 All Rights Reserved 由上海利云网络科技有限公司技术支持
    </div>
<script src="__CDN__/assets/js/jquery-1.11.0.min.js"></script>
<script src="__CDN__/assets//js/slide.js"></script>
<script>
   
    $(function(){
        var page_num = 0; //当前页
        $(".more").click(function(){
            page_num++;
            var th  =  $(".loading");
            var ths = $(this);
            $(this).css("display","none");
            th.css("display","block");
            $.ajax({
                url: "{:url('web/index')}",
                data: {
                    page_num: page_num,
                },
                type: "POST",
                dataType: "JSON",
                success: function(data) {
                    if(data.length == 0){
                        ths.css("display","block");
                        ths.html("没有更多了");
                        th.css("display","none");
                    }else{
                        var html = "";
                        for(var k in data) {
                            var toUrl = "{:url('web/detail',['id'=>'NMID','type'=>1])}";
                            toUrl = toUrl.replace('NMID',data[k].id);
                            html += '<li>' +
                                '<a href="'+toUrl+'" class="leftbox"  target="_blank">' +
                                '<div class="title">'+ data[k].title +'</div>' +
                                '<div class="content">'+ data[k].title +'</div>' +
                                '<div class="sub_content"><span class="addtime">'+ data[k].publishtime_text +
                                '</span><span class="readnum">'+ data[k].views +'&nbsp;阅读</span></div>' +
                                '</a>' +
                                '<img style="width: 150px;height: 150px;" src="'+ data[k].image +'">' +
                                '</li>';
                            $(".middle .zixun .left ul").append(html);
                            ths.css("display","block");
                            th.css("display","none");
                        }
                    }
                }
            })
        });
    });
    </script>
<!-- 最新的 Bootstrap 核心 JavaScript 文件 -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@3.3.7/dist/js/bootstrap.min.js" integrity="sha384-Tc5IQib027qvyjSMfHjOMaLkfuWVxZxUPnCJA7l2mCWNIpG9mGCD8wGNIcPD7Txa" crossorigin="anonymous"></script>
<script>
    var _hmt = _hmt || [];
    (function() {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?0ee6f31ca06e35148d2783344d22cd49";
      var s = document.getElementsByTagName("script")[0]; 
      s.parentNode.insertBefore(hm, s);
    })();
    </script>
    
</body>
</html>