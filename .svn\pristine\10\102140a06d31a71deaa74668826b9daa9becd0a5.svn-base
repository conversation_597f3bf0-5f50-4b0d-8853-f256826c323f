<?php

namespace app\api\controller;
use app\common\controller\Api;

use app\common\library\Tool;
use app\common\model\Company;
use app\common\model\Tables;
use think\Config;
use think\Db;
use think\Exception;
use think\exception\PDOException;

/**
 * 数据导入辅助
 *
 * @icon fa fa-circle-o
 */
class Import extends Api
{
    //如果$noNeedLogin为空表示所有接口都需要登录才能请求
    //如果$noNeedRight为空表示所有接口都需要验证权限才能请求
    //如果接口已经设置无需登录,那也就无需鉴权了
    //
    // 无需登录的接口,*表示全部
    protected $noNeedLogin = [''];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['*'];

    /**
     * 前台页面添加
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function web_add(){
        $comm = new Common();
        $path = $comm->upload(2);
        if(!$path)$this->error('上传失败');
        $tableId = $this->request->param('table_id');
        $companyId = $this->request->param('company_id');
        $date_type = $this->request->post('date_type');
        if(!$path)
            $this->error('表格路径不存在');
        if(!isset($tableId) && empty($tableId))
            $this->error('导入表匹配错误');
        $tool = new Tool();
        $res = $tool->tableId($tableId)
            ->companyId($companyId)
            ->type(2)
            ->flag(true)
            ->path($path)
            ->date_type($date_type)
            ->userId($this->auth->getUser()->id)->addData();
        if(!$res['status'])
            $this->error($res['error']);
        unlink(ROOT_PATH . '/public' .$path);
        $result = $tool->flag(true)->params($res['data'])
                    ->userId($this->auth->getUser()->id)->batch_insert();
        if ($result['status']) {
            $this->success(__('SUCCESS'),['data'=>$result['date']]);
        } else {
            $this->error($result['error']);
        }
        $this->error(__('Parameter %s can not be empty', ''));
    }

    /**
     * 添加
     */
    public function add()
    {
        $data = $this->request->post('data');
        if(empty($data))
            $this->error('数据不存在');
        $json = html_entity_decode($data);
        $arrays = json_decode($json,true);
        $option['user_id'] = $this->auth->getUser()->id;
        $tool = new Tool();
        $res = $tool->params($arrays)
            ->flag(true)
            ->userId($this->auth->getUser()->id)
            ->insert();
        if(!$res['status'])$this->error($res['error']);
        $this->success(__('SUCCESS'));

    }
    /**
     * 导入文件预览
     * @throws PDOException
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     * @throws \think\db\exception\BindParamException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function preview()
    {
        $table_id = $this->request->param('table_id');
        $company_id = $this->request->param('company_id');
        $path = $this->request->param('path');
        $year = $this->request->post('year');
        if(!$path)
            $this->error('表格路径不存在');
        if(!isset($table_id) && empty($table_id))
            $this->error('导入表匹配错误');
        if(!isset($company_id) && empty($company_id))
            $this->error('参数错误');
        if(!$year)
            $this->error('参数错误');
        $company_id = $this->request->param('company_id');
        $quarter = $this->request->post('quarter');
        $mouth = $this->request->post('mouth');
        $path = $this->request->param('path');
        $type = $this->request->param('type');
        $user_id = $this->auth->getUser()->id;
        $tool = new Tool();
        $res =  $tool->tableId($table_id)
            ->companyId($company_id)
            ->flag(true)
            ->date($year, $quarter, $mouth)
            ->type($type)
            ->path($path)
            ->userId($user_id)
            ->addData();
        if(is_file($path)){
            unlink(ROOT_PATH . '/public' . $path);
        }
        if(!$res['status']) $this->error($res['error']);
        $this->success(__('SUCCESS'),$res['data']);
    }


}
