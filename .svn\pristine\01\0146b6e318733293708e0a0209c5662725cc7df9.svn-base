<?php

namespace app\api\controller;


use app\common\controller\Api;
use app\common\model\Config;

/**
 * 每日签到
 */
class Leesign extends Api
{

    //如果$noNeedLogin为空表示所有接口都需要登录才能请求
    //如果$noNeedRight为空表示所有接口都需要验证权限才能请求
    //如果接口已经设置无需登录,那也就无需鉴权了
    //
    // 无需登录的接口,*表示全部
    protected $noNeedLogin = [''];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['*'];

    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = model('\app\common\model\Leesign');
    }

    /**
     * 签到页
     */
    public function index()
    {
        //签到配置
        $addonCfg = get_addon_config('leesign');

        $rule = nl2br($addonCfg['rule']);

        //当月的第一天
        $firstDate = date('Y-m-01', time());
        //当月的最后一天
        $lastDate = date('Y-m-d', strtotime("$firstDate + 1 month -1 day"));
        $where['sign_time'] = ['between', [$firstDate, $lastDate]];
        $where['uid'] = $this->auth->getUser()->id;
        $signlist = $this->model->where($where)->order('sign_time desc')->select();
        $days = [];
        foreach ($signlist as $k => $v)
        {

            if (date("Y-m") == substr($v['sign_time'], 0, 7))
            {
                $days[] = (int) substr($v['sign_time'], 8, 2);
            }
        }
        $this->success(__('SUCCESS'),['days' => $days, 'rule' => $rule]);
    }

    /**
     *  获取签到面板所需信息
     */
    public function getSignInfo()
    {
        //签到配置
        $addonCfg = get_addon_config('leesign');

        //当月的第一天
        $firstDate = date('Y-m-01', time());
        //当月的最后一天
        $lastDate = date('Y-m-d', strtotime("$firstDate + 1 month -1 day"));

        $w['sign_time'] = ['between', [$firstDate, $lastDate]];

        //获取兑换vip积分数
        $vip_num = Config::where('group','=','user')
            ->field('value')
            ->where('name','=','vip')->find();

        $w['uid'] =  $this->auth->getUser()->id;

        //本月签到数
        $len = $this->model->where($w)->order('sign_time desc')->count();

        //额外获得积分数
        $extra_total = $this->model->where($w)->sum('sign_extra_reward');
        $extra_total = $extra_total ? $extra_total : 0;

        //连续签到数
        //连续签到数
        $row = $this->model->where($w)->order('sign_time desc')->field('max_sign')->find();
        $lianxu = $row ? $row['max_sign'] : 0;

        $signlist = $this->model->where($w)->order('sign_time desc')->field('sign_time,sign_reward,sign_extra_reward,type')->select();

        $data = $signlist ? $signlist : __('sign empty');

        $this->success('签到奖励信息',['length' => $len,'signnum'=>$addonCfg['signnum'],
            'extra_total' => $extra_total,
            'lianxu' => $lianxu,'vip_num'=>(int)$vip_num->value,
            'data' => $data]);
    }

    /**
     * 签到逻辑
     */
    public function sign()
    {
        $curr = date('Y-m-d', time());
        $ww['uid'] = $this->auth->id;
        $repeat = $this->model->where("DATE_FORMAT(sign_time,'%Y-%m-%d') = '$curr'")
                ->where('type','=',0)
                ->where($ww)->find();
        if ($repeat)
        {
            $this->success('已签到',[]);
        }

        //签到配置
        $config = get_addon_config('leesign');

        //当月的第一天
        $firstDate = date('Y-m-01', time());
        //当月的最后一天
        $lastDate = date('Y-m-d', strtotime("$firstDate + 1 month -1 day"));

        $w['sign_time'] = ['between', [$firstDate, $lastDate]];
        $w['uid'] = $this->auth->getUser()->id;

        $signList = $this->model->where($w)->where('type','=',0)->order('sign_time desc')->select();
        $len = count($signList, 0);

        if ($signList && $len >= 1)
        {
            $lianxu = (date("Y-m-d", strtotime($signList[0]['sign_time'] . "+ 1 day")) != date("Y-m-d", time())) ? false : true;
        }
        else
        {
            $lianxu = false;
        }

        //处理逻辑：如果上次签到的日期和这次签到的日期相差不是1天，那么本次签到就不是连续签到。
        $max_sign_num = $lianxu ? $signList[0]['max_sign'] + 1 : 1;

        $score = $config['signnum'];

        //连续签到奖励规则 - 周期奖励
        $zhouqi = $config['types'];

        //当月连续签到所获得的所有额外奖励
        $extra = 0;

        //当天是否触发连续签到的额外奖励
        $extra_reward = 0;

        //开启了连续签到奖励
        if ($config['signstatus'] == 1)
        {
            //计算连续签到带来的额外奖励
            foreach ($zhouqi as $k => $v)
            {
                foreach ($signList as $key => $val)
                {
                    if ($k == $val['max_sign'])
                    {
                        $extra += $v;
                        break;
                    }
                }

                if ($k == $max_sign_num)
                {
                    $extra_reward += $v;
                }
            }
        }

        $data = [
            'sign_ip'           => $this->request->ip(),
            'uid'               => $this->auth->id,
            'sign_time'         => date('Y-m-d H:i:s'),
            'sign_reward'       => $score,
            'sign_extra_reward' => $extra_reward,
            'max_sign'          => $max_sign_num,
        ];

        if ($this->model->insert($data))
        {
            //签到积分增加日志
            \app\common\model\User::score($score, $this->auth->id, '连续签到奖励');

            if ($extra_reward > 0)
            {
                //额外获得积分记录
                \app\common\model\User::score($extra_reward, $this->auth->id, '额外签到奖励');
            }
            $this->success('签到成功',
                ['max_sign' => $max_sign_num, 'month_sign_num' => $len,
                    'reward' => ($score + $extra_reward)]);
        }
        else
        {
            //__('sign failed')
            $this->success('签到失败',[]);
        }
    }
    /**
     * 分享加积分逻辑
     */
    public function share()
    {
        $curr = date('Y-m-d', time());
        $ww['uid'] = $this->auth->id;
        $repeat = $this->model->where("DATE_FORMAT(sign_time,'%Y-%m-%d') = '$curr'")
            ->where('type','=',1)
            ->where($ww)->find();
        if ($repeat)
        {
            $this->success('已分享',[]);
        }

        //签到配置
        $config = get_addon_config('leesign');

        //当月的第一天
        $firstDate = date('Y-m-01', time());
        //当月的最后一天
        $lastDate = date('Y-m-d', strtotime("$firstDate + 1 month -1 day"));

        $w['sign_time'] = ['between', [$firstDate, $lastDate]];
        $w['uid'] = $this->auth->getUser()->id;

        $signList = $this->model->where($w)
            ->where('type','=',1)
            ->order('sign_time desc')->select();
        $len = count($signList, 0);

        if ($signList && $len >= 1)
        {
            $lianxu = (date("Y-m-d", strtotime($signList[0]['sign_time'] . "+ 1 day")) != date("Y-m-d", time())) ? false : true;
        }
        else
        {
            $lianxu = false;
        }

        //处理逻辑：如果上次签到的日期和这次签到的日期相差不是1天，那么本次签到就不是连续签到。
        $max_sign_num = $lianxu ? $signList[0]['max_sign'] + 1 : 1;

        $score = $config['signnum'];

        //连续签到奖励规则 - 周期奖励
        //$zhouqi = $config['types'];

        //当月连续签到所获得的所有额外奖励
        //$extra = 0;

        //当天是否触发连续签到的额外奖励
        $extra_reward = 0;

        //开启了连续签到奖励
//        if ($config['signstatus'] == 1)
//        {
//            //计算连续签到带来的额外奖励
//            foreach ($zhouqi as $k => $v)
//            {
//                foreach ($signList as $key => $val)
//                {
//                    if ($k == $val['max_sign'])
//                    {
//                        $extra += $v;
//                        break;
//                    }
//                }
//
//                if ($k == $max_sign_num)
//                {
//                    $extra_reward += $v;
//                }
//            }
//        }

        $data = [
            'sign_ip'           => $this->request->ip(),
            'uid'               => $this->auth->id,
            'sign_time'         => date('Y-m-d H:i:s'),
            'sign_reward'       => $score,
            'sign_extra_reward' => $extra_reward,
            'max_sign'          => $max_sign_num,
            'type'              =>1
        ];

        if ($this->model->insert($data))
        {
            //签到积分增加日志
            \app\common\model\User::score($score, $this->auth->id, '连续签到奖励');

//            if ($extra_reward > 0)
//            {
//                //额外获得积分记录
//                \app\common\model\User::score($extra_reward, $this->auth->id, '额外签到奖励');
//            }
            $this->success('分享成功',
                ['max_sign' => $max_sign_num, 'month_sign_num' => $len,
                    'reward' => ($score + $extra_reward)]);
        }
        else
        {
            //__('sign failed')
            $this->success('分享失败',[]);
        }
    }

    /**
     * 积分兑换
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function vipExchange(){

        $user = $this->auth->getUser();
        //如果兑换的时候没有过去
        if($user->vip_expirationtime>time()){//没有过期
            if($user->is_vip>0){$this->success('已经是vip不能兑换',[]);}
        }
        //获取积分数
        $vip_num = Config::where('group','=','user')
            ->field('value')
            ->where('name','=','vip')->find();
        //如果用户的积分小于兑换的积分数说明兑换不了
        if($user->score < $vip_num->value){
            $this->success('积分不够',[]);
        }
        //兑换逻辑
        \app\common\model\User::exchangeVip($vip_num->value,$user->id,'积分兑换vip');
        $this->success('兑换成功',[]);
    }



}
