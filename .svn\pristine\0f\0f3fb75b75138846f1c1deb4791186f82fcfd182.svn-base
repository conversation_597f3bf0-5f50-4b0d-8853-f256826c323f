<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <input type="hidden" value="{$row.id}" id="archive-id"/>
    <input type="hidden" name="row[style]" value="{$row.style}"/>
    <div class="row">
        <div class="col-md-9 col-sm-12">
            <div class="panel panel-default panel-intro">
                <div class="panel-heading">
                    <ul class="nav nav-tabs">
                        <li class="active"><a href="#basic" data-toggle="tab">基础信息</a></li>
                    </ul>
                </div>
                <div class="panel-body">

                    <div id="myTabContent" class="tab-content">
                        <div class="tab-pane fade active in" id="basic">
                            <div class="form-group">
                                <label for="c-channel_id" class="control-label col-xs-12 col-sm-2">{:__('Channel_id')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <select id="c-channel_id" data-rule="required" class="form-control selectpicker" data-live-search="true" name="row[channel_id]">
                                        {$channelOptions}
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="c-channel_id" class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <input type="text" class="form-control selectpage" data-source="user/user/index" placeholder="发布会员,可为空" data-field="nickname" name="row[user_id]" value="{$row.user_id}"/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="c-special_id" class="control-label col-xs-12 col-sm-2">{:__('Special_id')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <input type="text" class="form-control selectpage" data-source="cms/special/index" placeholder="所属专题,可为空" data-field="title" name="row[special_id]" value="{$row.special_id}"/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="c-title" class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <div class="input-group">
                                        <input id="c-title" data-rule="required" class="form-control {:$row.style_bold?'text-bold':''}" name="row[title]" type="text" value="{$row.title|htmlentities}" style="color:{:$row.style_color?$row.style_color:'inherit'};">
                                        <span class="input-group-btn">
                                        <button class="btn btn-default btn-bold {:$row.style_bold?'text-bold active':''}" style="margin:0 1px;" type="button">粗</button>
                                        <button type="button" class="btn btn-default btn-color colorpicker {:$row.style_color?'active':''}" style="padding:0;margin-left:1px;" title="选择标题颜色"><img src="__CDN__/assets/addons/cms/img/colorful.png" height="29" alt=""></button>
                                        <span class="msg-box n-right" for="c-title"></span>
                                    </span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="c-image" class="control-label col-xs-12 col-sm-2">{:__('Image')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <div class="input-group">
                                        <input id="c-image" class="form-control" size="50" name="row[image]" type="text" value="{$row.image|htmlentities}">
                                        <div class="input-group-addon no-border no-padding">
                                            <span><button type="button" id="plupload-image" class="btn btn-danger plupload" data-input-id="c-image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                            <span><button type="button" id="fachoose-image" class="btn btn-primary fachoose" data-input-id="c-image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                                        </div>
                                        <span class="msg-box n-right" for="c-image"></span>
                                    </div>
                                    <ul class="row list-inline plupload-preview" id="p-image"></ul>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2">{:__('Banner')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <div class="input-group">
                                        <input id="c-banner"  class="form-control" size="50" name="row[banner]" type="text" value="{$row.banner|htmlentities}">
                                        <div class="input-group-addon no-border no-padding">
                                            <span><button type="button" id="plupload-banner" class="btn btn-danger plupload" data-input-id="c-banner" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-banner"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                            <span><button type="button" id="fachoose-banner" class="btn btn-primary fachoose" data-input-id="c-banner" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                                        </div>
                                        <span class="msg-box n-right" for="c-banner"></span>
                                    </div>
                                    <ul class="row list-inline plupload-preview" id="p-banner"></ul>
                                </div>
                            </div>

                            <!--<div class="form-group">-->
                                <!--<label for="c-tags" class="control-label col-xs-12 col-sm-2">{:__('Tags')}:</label>-->
                                <!--<div class="col-xs-12 col-sm-8">-->
                                    <!--<input id="c-tags" data-rule="" class="form-control" placeholder="输入后空格确认" name="row[tags]" type="text" value="{$row.tags|htmlentities}">-->
                                <!--</div>-->
                            <!--</div>-->

                            <!--<div class="form-group">-->
                                <!--<label for="c-diyname" class="control-label col-xs-12 col-sm-2">{:__('Diyname')}:</label>-->
                                <!--<div class="col-xs-12 col-sm-8">-->
                                    <!--<div class="input-group">-->
                                        <!--<div class="input-group-btn">-->
                                            <!--<button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">{:addon_url('cms/archives/index', [':diyname'=>''])}</button>-->
                                        <!--</div>-->
                                        <!--<input type="text" id="c-diyname" data-rule="diyname" name="row[diyname]" class="form-control" placeholder="请输入自定义的名称,为空将使用主键ID" value="{$row.diyname}"/>-->
                                    <!--</div>-->
                                <!--</div>-->
                            <!--</div>-->
                            <div class="form-group">
                                <label for="c-content" class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <textarea id="c-content" data-rule="required" class="form-control editor" name="row[content]" rows="15">{$row.content|htmlentities}</textarea>
                                    <div style="margin-top:5px;">
                                        <a href="javascript:" class="btn btn-xs btn-primary btn-legal">{:__('Check content is legal')}</a>
                                        <a href="javascript:" class="btn btn-xs btn-danger btn-keywords">{:__('Get the keyword and description')}</a>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="c-seotitle" class="control-label col-xs-12 col-sm-2">{:__('Seotitle')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <input id="c-seotitle" data-rule="" class="form-control" name="row[seotitle]" type="text" value="{$row.seotitle|htmlentities}" placeholder="为空时将使用文档标题">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="c-keywords" class="control-label col-xs-12 col-sm-2">{:__('Keywords')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <input id="c-keywords" data-rule="" class="form-control" name="row[keywords]" type="text" value="{$row.keywords|htmlentities}">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="c-description" class="control-label col-xs-12 col-sm-2">{:__('Description')}:</label>
                                <div class="col-xs-12 col-sm-8">
                                    <textarea id="c-description" cols="60" rows="5" data-rule="" class="form-control" name="row[description]">{$row.description|htmlentities}</textarea>
                                </div>
                            </div>
                            <div id="extend"></div>
                        </div>
                    </div>
                    <div class="form-group layer-footer">
                        <label class="control-label col-xs-12 col-sm-2"></label>
                        <div class="col-xs-12 col-sm-8">
                            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
                            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                        </div>
                    </div>
                </div>

            </div>

        </div>
        <div class="col-md-3 col-sm-12">
            <div class="panel panel-default panel-intro">
                <div class="panel-heading">
                    <div class="panel-lead"><em>相关信息</em></div>
                </div>
                <div class="panel-body">
                    <div class="tab-content">
                        <div class="tab-pane fade active in">
                            <div class="form-group">
                                <label for="c-views" class="control-label col-xs-12 col-sm-2 col-md-3">{:__('Views')}:</label>
                                <div class="col-xs-12 col-sm-8 col-md-8">
                                    <div class="input-group margin-bottom-sm">

                                        <input id="c-views" data-rule="required" class="form-control" name="row[views]" placeholder="{:__('Views')}" type="number" value="{$row.views}">
                                        <span class="input-group-addon"><i class="fa fa-eye text-success"></i></span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="c-comments" class="control-label col-xs-12 col-sm-2 col-md-3">{:__('Comments')}:</label>
                                <div class="col-xs-12 col-sm-8 col-md-8">
                                    <div class="input-group margin-bottom-sm">

                                        <input id="c-comments" data-rule="required" class="form-control" name="row[comments]" placeholder="{:__('Comments')}" type="number" value="{$row.comments}">
                                        <span class="input-group-addon"><i class="fa fa-comment text-info"></i></span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2 col-sm-3">{:__('Collections')}:</label>
                                <div class="col-xs-12 col-sm-8 col-md-8">
                                    <div class="input-group margin-bottom-sm">
                                        <input id="c-collections" data-rule="required" class="form-control" name="row[collections]" placeholder="{:__('Collections')}" type="number" value="{$row.collections}">
                                        <span class="input-group-addon"><i class="fa fa-comment text-info"></i></span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="c-likes" class="control-label col-xs-12 col-sm-2 col-md-3">{:__('Likes')}:</label>
                                <div class="col-xs-12 col-sm-8 col-md-8">
                                    <div class="input-group margin-bottom-sm">

                                        <input id="c-likes" data-rule="required" class="form-control" name="row[likes]" placeholder="{:__('Likes')}" type="number" value="{$row.likes}">
                                        <span class="input-group-addon"><i class="fa fa-thumbs-up text-danger"></i></span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="c-dislikes" class="control-label col-xs-12 col-sm-2 col-md-3">{:__('Dislikes')}:</label>
                                <div class="col-xs-12 col-sm-8 col-md-8">
                                    <div class="input-group margin-bottom-sm">
                                        <input id="c-dislikes" data-rule="required" class="form-control" name="row[dislikes]" placeholder="{:__('Dislikes')}" type="number" value="{$row.dislikes}">
                                        <span class="input-group-addon"><i class="fa fa-thumbs-down text-gray"></i></span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="c-weigh" class="control-label col-xs-12 col-sm-2 col-md-3">{:__('Weigh')}:</label>
                                <div class="col-xs-12 col-sm-8 col-md-8">
                                    <input id="c-weigh" data-rule="required" class="form-control" name="row[weigh]" placeholder="{:__('Weigh')}" type="number" value="{$row.weigh}">
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="panel panel-default panel-intro">
                <div class="panel-heading">
                    <div class="panel-lead"><em>状态</em></div>
                </div>
                <div class="panel-body">
                    <div class="tab-content">
                        <div class="tab-pane fade active in">
                            <div class="form-group">
                                <label for="c-flag" class="control-label col-xs-12 col-sm-2 col-md-3">{:__('Flag')}:</label>
                                <div class="col-xs-12 col-sm-8 col-md-8">

                                    <select id="c-flag" class="form-control selectpicker" multiple="" name="row[flag][]">
                                        {foreach name="flagList" item="vo"}
                                        <option value="{$key}" {in name="key" value="$row.flag" }selected{/in}>{$vo}</option>
                                        {/foreach}
                                    </select>

                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2 col-md-3">{:__('Status')}:</label>
                                <div class="col-xs-12 col-sm-8 col-md-8">
                                    <select id="c-status" class="form-control selectpicker" name="row[status]">
                                        {foreach name="statusList" item="vo"}
                                        <option value="{$key}" {in name="key" value="$row.status" }selected{/in}>{$vo}</option>
                                        {/foreach}
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2 col-md-3">{:__('Createtime')}:</label>
                                <div class="col-xs-12 col-sm-8 col-md-8">
                                    <div class='input-group date datetimepicker'>
                                        <input type='text' name="row[createtime]" data-rule="required" data-date-format="YYYY-MM-DD HH:mm:ss" value="{$row.createtime|datetime}" class="form-control datetimepicker"/>
                                        <span class="input-group-addon">
                                            <span class="fa fa-calendar"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="control-label col-xs-12 col-sm-2 col-md-3">{:__('Publishtime')}:</label>
                                <div class="col-xs-12 col-sm-8 col-md-8">
                                    <div class='input-group date datetimepicker'>
                                        <input type='text' name="row[publishtime]" data-rule="required(isnormal)" data-date-format="YYYY-MM-DD HH:mm:ss" value="{$row.publishtime_text}" class="form-control datetimepicker"/>
                                        <span class="input-group-addon">
                                            <span class="fa fa-calendar"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>