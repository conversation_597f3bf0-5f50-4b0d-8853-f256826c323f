// Korean (ko)
plupload.addI18n({"Stop Upload":"업로드 중지","Upload URL might be wrong or doesn't exist.":"업로드할 URL이 존재하지 않습니다.","tb":"tb","Size":"크기","Close":"닫기","You must specify either browse_button or drop_element.":"browse_button 이나 drop_element 둘 중 하나를 지정해 주세요.","Init error.":"초기화 오류.","Add files to the upload queue and click the start button.":"파일을 업로드 큐에 추가한 후 시작 버튼을 클릭하십시오.","List":"목록","Filename":"파일명","%s specified, but cannot be found.":"%s 가 지정됐지만, 찾을 수 없습니다.","Image format either wrong or not supported.":"지원되지 않는 이미지 형식입니다.","Status":"상태","HTTP Error.":"HTTP 오류.","Start Upload":"업로드 시작","Error: File too large:":"오류: 파일 크기가 너무 큽니다.","kb":"kb","Duplicate file error.":"파일 중복 오류.","File size error.":"파일 크기 오류.","N/A":"N/A","gb":"gb","Error: Invalid file extension:":"오류: 허용되지 않은 확장자입니다.","Select files":"파일 선택","%s already present in the queue.":"%s 파일이 이미 대기열에 존재합니다.","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"해상도 지원 범위를 초과했습니다! <b>%s</b> 런타임은 %wx%hpx 이상의 이미지를 지원합니다.","File: %s":"파일: %s","b":"b","Uploaded %d/%d files":"%d / %d 파일 업로드 완료","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"업로드 가능한 파일의 수는 %d 입니다. 불필요한 파일은 삭제되었습니다.","%d files queued":"%d 파일이 추가됨","File: %s, size: %d, max file size: %d":"파일: %s, 크기: %d, 최대 파일 크기: %d","Thumbnails":"미리보기","Drag files here.":"이곳에 파일을 드래그 하세요.","Runtime ran out of available memory.":"런타임 메모리가 부족합니다.","File count error.":"파일 갯수 오류.","File extension error.":"파일 확장자 오류.","mb":"mb","Add Files":"파일 추가"});